#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的DeepSeek API测试
"""

import os
from dotenv import load_dotenv
from openai import OpenAI

def test_api():
    """测试API连接"""
    load_dotenv()
    
    api_key = os.getenv("API_KEY", "")
    base_url = os.getenv("BASE_URL", "https://api.deepseek.com/v1")
    model = os.getenv("MODEL", "deepseek-chat")
    
    print(f"API Key: {api_key[:10]}..." if api_key else "No API Key")
    print(f"Base URL: {base_url}")
    print(f"Model: {model}")
    
    try:
        client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=50
        )
        
        print("✅ Success!")
        print(f"Response: {response.choices[0].message.content}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_api()
